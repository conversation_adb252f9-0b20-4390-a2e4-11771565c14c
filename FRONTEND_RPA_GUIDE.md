# 前端RPA自动化使用指南

## 🎯 功能概述

本系统提供了一个纯前端的RPA（机器人流程自动化）解决方案，无需后端服务，直接在浏览器中实现自动化操作。

### 核心功能
1. **表单数据收集** - 用户填写登录信息和录入数据
2. **自动打开目标系统** - 在新窗口中打开目标系统
3. **自动登录** - 自动填写用户名密码并登录
4. **自动数据录入** - 根据表单数据自动填写目标系统的表单
5. **实时状态监控** - 显示执行进度和日志信息

## 🚀 快速开始

### 1. 启动系统
```bash
pnpm dev
```

### 2. 访问界面
打开浏览器访问: http://localhost:5173

### 3. 选择"前端RPA自动化"菜单

## 📋 使用步骤

### 步骤1: 填写登录信息
- **用户名**: 目标系统的登录用户名
- **密码**: 目标系统的登录密码

### 步骤2: 填写录入数据
- **姓名**: 要录入的姓名信息
- **邮箱**: 要录入的邮箱地址
- **手机号**: 要录入的手机号码
- **部门**: 选择所属部门
- **项目名称**: 要录入的项目名称
- **项目类型**: 选择项目类型
- **项目描述**: 项目的详细描述

### 步骤3: 配置RPA参数
- **执行延迟**: 操作之间的延迟时间（500-3000ms）
- **超时时间**: 单个操作的超时时间（5-30秒）

### 步骤4: 测试连接
点击"测试连接"按钮，确保目标系统可以正常访问

### 步骤5: 启动RPA
点击"启动RPA自动化"按钮开始执行

## 🔧 技术原理

### 1. 窗口操作
- 使用 `window.open()` 打开目标系统
- 通过窗口引用操作目标页面

### 2. 脚本注入
- 动态创建 `<script>` 标签
- 注入自动化脚本到目标页面
- 执行DOM操作和事件触发

### 3. 元素查找策略
```javascript
// 用户名输入框查找策略
const usernameSelectors = [
  'input[name="username"]',
  'input[name="user"]',
  'input[name="account"]',
  'input[placeholder*="用户名"]',
  'input[placeholder*="账号"]',
  'input[type="text"]:first-of-type'
];
```

### 4. 事件模拟
```javascript
// 填写输入框并触发事件
element.value = value;
element.dispatchEvent(new Event('input', { bubbles: true }));
element.dispatchEvent(new Event('change', { bubbles: true }));
```

## ⚠️ 重要注意事项

### 浏览器设置
1. **允许弹出窗口**: 必须允许本站点的弹出窗口
2. **禁用弹出窗口拦截器**: 确保目标窗口能正常打开
3. **同源策略限制**: 跨域页面可能无法完全控制

### 目标系统要求
1. **可访问性**: 目标系统必须可以通过浏览器正常访问
2. **标准HTML表单**: 使用标准的HTML表单元素
3. **无复杂验证**: 避免复杂的验证码或安全机制

### 执行环境
1. **网络稳定**: 确保网络连接稳定
2. **浏览器兼容**: 推荐使用Chrome或Edge浏览器
3. **页面不关闭**: 执行过程中不要关闭弹出的窗口

## 🛠 故障排除

### 常见问题

#### 1. 无法打开目标窗口
**原因**: 浏览器阻止了弹出窗口
**解决**: 
- 点击地址栏的弹出窗口图标，选择"始终允许"
- 在浏览器设置中添加站点到弹出窗口白名单

#### 2. 登录失败
**原因**: 
- 用户名密码错误
- 目标系统登录表单结构特殊
- 存在验证码

**解决**:
- 检查用户名密码是否正确
- 手动完成登录，系统会自动检测登录成功
- 如有验证码，需要手动处理

#### 3. 数据录入失败
**原因**:
- 目标系统表单字段名称不匹配
- 页面加载不完整

**解决**:
- 增加执行延迟时间
- 手动完成数据录入
- 检查目标系统的表单结构

#### 4. 跨域限制
**原因**: 浏览器同源策略限制
**解决**:
- 系统会自动处理大部分跨域情况
- 某些操作可能需要手动完成

### 调试方法

#### 1. 查看执行日志
系统会实时显示执行日志，包括：
- 操作步骤
- 成功/失败状态
- 错误信息

#### 2. 浏览器开发者工具
在目标窗口中按F12打开开发者工具：
- 查看Console中的RPA日志
- 检查Network请求
- 查看Elements结构

#### 3. 手动干预
如果自动化失败，可以：
- 手动完成登录
- 手动填写表单
- 系统会自动检测并继续

## 🔒 安全考虑

### 1. 数据安全
- 密码等敏感信息仅在浏览器内存中处理
- 不会发送到任何服务器
- 页面刷新后数据自动清除

### 2. 系统安全
- 仅操作用户主动打开的窗口
- 不会访问其他网站或系统
- 遵循浏览器安全策略

### 3. 使用建议
- 仅在可信的内网环境中使用
- 定期更改登录密码
- 不要在公共计算机上使用

## 📈 性能优化

### 1. 执行速度
- 适当调整执行延迟
- 避免过快的操作频率
- 等待页面完全加载

### 2. 稳定性
- 增加重试机制
- 设置合理的超时时间
- 处理异常情况

### 3. 兼容性
- 支持多种元素选择器
- 兼容不同的表单结构
- 适配各种页面布局

## 🔄 扩展开发

### 1. 添加新的字段映射
```javascript
const fieldMappings = [
  { 
    key: 'newField', 
    selectors: [
      'input[name="newField"]', 
      'input[placeholder*="新字段"]'
    ] 
  }
];
```

### 2. 自定义选择器策略
```javascript
const customSelectors = {
  username: ['input[data-testid="username"]'],
  password: ['input[data-testid="password"]']
};
```

### 3. 添加新的操作步骤
```javascript
// 在executeRpaFlow函数中添加新步骤
updateStatus(5, '执行自定义操作...', 'info');
await performCustomAction();
updateStatus(5, '自定义操作完成', 'success');
```

## 📞 技术支持

如遇到问题，请：
1. 查看执行日志中的错误信息
2. 检查浏览器控制台输出
3. 确认目标系统的可访问性
4. 尝试手动操作验证流程

---

**免责声明**: 本工具仅供学习和内部使用，请确保遵守相关法律法规和目标系统的使用条款。
