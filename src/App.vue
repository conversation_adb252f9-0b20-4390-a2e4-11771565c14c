<template>
  <div id="app">
    <!-- elementPlus组件汉化 -->
    <el-config-provider :locale="localeZH">
    </el-config-provider>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import localeZH from "element-plus/es/locale/lang/zh-cn"
</script>

<style lang="scss">

#app {
  min-height: 100vh;
  background-color: $bg-secondary;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-menu {
  background-color: $bg-primary;
  border-bottom: 1px solid $border-light;

  :deep(.el-menu-item) {
    font-size: $font-size-md;
    font-weight: $font-weight-medium;

    &.is-active {
      color: $primary-color;
      border-bottom-color: $primary-color;
    }

    &:hover {
      color: $primary-color;
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
}
</style>
